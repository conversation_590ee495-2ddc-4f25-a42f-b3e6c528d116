import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  ScrollView,
} from 'react-native';
import { radioApi } from '../services/radioApi';

export default function CategoriesScreen({ navigation }) {
  const [countries, setCountries] = useState([]);
  const [genres, setGenres] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('countries'); // 'countries' or 'genres'

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [countriesData, genresData] = await Promise.all([
        radioApi.getCountries(),
        radioApi.getTags(50)
      ]);
      
      // Filter and sort countries
      const filteredCountries = countriesData
        .filter(country => country.stationcount > 0)
        .sort((a, b) => b.stationcount - a.stationcount)
        .slice(0, 50);
      
      // Filter and sort genres
      const filteredGenres = genresData
        .filter(genre => genre.stationcount > 0)
        .sort((a, b) => b.stationcount - a.stationcount);

      setCountries(filteredCountries);
      setGenres(filteredGenres);
    } catch (error) {
      Alert.alert('Hata', 'Kategoriler yüklenirken bir hata oluştu');
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCountryPress = (country) => {
    navigation.navigate('StationList', { 
      type: 'country', 
      value: country.name,
      title: `${country.name} İstasyonları`
    });
  };

  const handleGenrePress = (genre) => {
    navigation.navigate('StationList', { 
      type: 'genre', 
      value: genre.name,
      title: `${genre.name} İstasyonları`
    });
  };

  const renderCountry = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => handleCountryPress(item)}
    >
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryCount}>{item.stationcount} istasyon</Text>
      </View>
      <Text style={styles.arrow}>›</Text>
    </TouchableOpacity>
  );

  const renderGenre = ({ item }) => (
    <TouchableOpacity
      style={styles.categoryCard}
      onPress={() => handleGenrePress(item)}
    >
      <View style={styles.categoryInfo}>
        <Text style={styles.categoryName}>{item.name}</Text>
        <Text style={styles.categoryCount}>{item.stationcount} istasyon</Text>
      </View>
      <Text style={styles.arrow}>›</Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Kategoriler yükleniyor...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Tab Buttons */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'countries' && styles.activeTab]}
          onPress={() => setActiveTab('countries')}
        >
          <Text style={[styles.tabText, activeTab === 'countries' && styles.activeTabText]}>
            🌍 Ülkeler
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'genres' && styles.activeTab]}
          onPress={() => setActiveTab('genres')}
        >
          <Text style={[styles.tabText, activeTab === 'genres' && styles.activeTabText]}>
            🎵 Türler
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {activeTab === 'countries' ? (
        <FlatList
          data={countries}
          renderItem={renderCountry}
          keyExtractor={(item) => item.name}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      ) : (
        <FlatList
          data={genres}
          renderItem={renderGenre}
          keyExtractor={(item) => item.name}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    margin: 16,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabButton: {
    flex: 1,
    padding: 12,
    alignItems: 'center',
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: '#007AFF',
  },
  tabText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#666',
  },
  activeTabText: {
    color: '#fff',
  },
  listContainer: {
    paddingHorizontal: 16,
  },
  categoryCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 10,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  categoryCount: {
    fontSize: 14,
    color: '#666',
  },
  arrow: {
    fontSize: 20,
    color: '#ccc',
    marginLeft: 10,
  },
});

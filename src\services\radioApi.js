// Radio Browser API base URL
const BASE_URL = 'https://de1.api.radio-browser.info';

// Fetch wrapper function
const apiRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RadioBrowserApp/1.0.0',
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.json();
  } catch (error) {
    console.error('API Request Error:', error);
    throw error;
  }
};

// API Functions
export const radioApi = {
  // GET Endpoints

  // Get API information
  getApiInfo: async () => {
    try {
      return await apiRequest('/');
    } catch (error) {
      console.error('API Info Error:', error);
      throw error;
    }
  },

  // Get top/popular stations
  getTopStations: async (limit = 20) => {
    try {
      return await apiRequest(`/json/stations/topvote/${limit}`);
    } catch (error) {
      console.error('Top Stations Error:', error);
      throw error;
    }
  },

  // Search stations by name
  searchStations: async (query, limit = 20) => {
    try {
      return await apiRequest(`/json/stations/byname/${encodeURIComponent(query)}?limit=${limit}`);
    } catch (error) {
      console.error('Search Stations Error:', error);
      throw error;
    }
  },

  // Get stations by country
  getStationsByCountry: async (country, limit = 20) => {
    try {
      return await apiRequest(`/json/stations/bycountry/${encodeURIComponent(country)}?limit=${limit}`);
    } catch (error) {
      console.error('Stations by Country Error:', error);
      throw error;
    }
  },

  // Get stations by genre/tag
  getStationsByGenre: async (genre, limit = 20) => {
    try {
      return await apiRequest(`/json/stations/bytag/${encodeURIComponent(genre)}?limit=${limit}`);
    } catch (error) {
      console.error('Stations by Genre Error:', error);
      throw error;
    }
  },

  // Get all countries
  getCountries: async () => {
    try {
      return await apiRequest('/json/countries');
    } catch (error) {
      console.error('Countries Error:', error);
      throw error;
    }
  },

  // Get all tags/genres
  getTags: async (limit = 100) => {
    try {
      return await apiRequest(`/json/tags?limit=${limit}`);
    } catch (error) {
      console.error('Tags Error:', error);
      throw error;
    }
  },

  // Advanced search
  advancedSearch: async (searchParams) => {
    try {
      return await apiRequest('/json/stations/search', {
        method: 'POST',
        body: JSON.stringify(searchParams),
      });
    } catch (error) {
      console.error('Advanced Search Error:', error);
      throw error;
    }
  },

  // Click/Listen to station (for statistics)
  clickStation: async (stationUuid) => {
    try {
      return await apiRequest(`/json/url/${stationUuid}`);
    } catch (error) {
      console.error('Click Station Error:', error);
      throw error;
    }
  },

  // Vote for station
  voteStation: async (stationUuid) => {
    try {
      return await apiRequest(`/json/vote/${stationUuid}`);
    } catch (error) {
      console.error('Vote Station Error:', error);
      throw error;
    }
  }
};

export default radioApi;

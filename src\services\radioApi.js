// Backend API base URL
const BASE_URL = 'http://localhost:3000';

// Fetch wrapper function with detailed logging
const apiRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RadioBrowserApp/1.0.0',
    },
    ...options,
  };

  console.log('🔗 API Request:', {
    url,
    method: config.method || 'GET',
    timestamp: new Date().toISOString()
  });

  try {
    const startTime = Date.now();
    const response = await fetch(url, config);
    const endTime = Date.now();

    console.log('📡 API Response:', {
      url,
      status: response.status,
      statusText: response.statusText,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ API Data received:', {
      url,
      dataLength: Array.isArray(data) ? data.length : 'object',
      firstItem: Array.isArray(data) && data.length > 0 ? data[0] : null,
      timestamp: new Date().toISOString()
    });

    return data;
  } catch (error) {
    console.error('❌ API Request Error:', {
      url,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

// Backend API Functions - GET & POST Endpoints
export const radioApi = {

  // GET Endpoints

  // 1. Get top/popular stations
  getTopStations: async (limit = 10) => {
    console.log('🎯 Getting Top Stations...');
    try {
      const data = await apiRequest(`/api/stations/top?limit=${limit}`);
      console.log('🎵 Top Stations Success:', data?.length || 0, 'stations received');
      return data;
    } catch (error) {
      console.error('❌ Top Stations Failed:', error.message);
      throw error;
    }
  },

  // 2. Search stations by name
  searchStations: async (query, limit = 10) => {
    console.log('🔍 Searching Stations for:', query);
    try {
      const data = await apiRequest(`/api/stations/search/${encodeURIComponent(query)}?limit=${limit}`);
      console.log('🎵 Search Success:', data?.length || 0, 'stations found for:', query);
      return data;
    } catch (error) {
      console.error('❌ Search Failed for:', query, error.message);
      throw error;
    }
  },

  // 3. Get stations by country
  getStationsByCountry: async (country, limit = 10) => {
    console.log('🌍 Getting Stations for Country:', country);
    try {
      const data = await apiRequest(`/api/stations/country/${encodeURIComponent(country)}?limit=${limit}`);
      console.log('🎵 Country Stations Success:', data?.length || 0, 'stations for:', country);
      return data;
    } catch (error) {
      console.error('❌ Country Stations Failed for:', country, error.message);
      throw error;
    }
  },

  // POST Endpoints

  // 4. Add station to favorites
  addToFavorites: async (stationId, userId, stationName) => {
    console.log('❤️ Adding to Favorites:', { stationId, userId, stationName });
    try {
      const data = await apiRequest('/api/stations/favorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stationId,
          userId,
          stationName
        })
      });
      console.log('✅ Added to Favorites Success:', data);
      return data;
    } catch (error) {
      console.error('❌ Add to Favorites Failed:', error.message);
      throw error;
    }
  },

  // 5. Remove station from favorites
  removeFromFavorites: async (stationId, userId) => {
    console.log('💔 Removing from Favorites:', { stationId, userId });
    try {
      const data = await apiRequest('/api/stations/unfavorite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stationId,
          userId
        })
      });
      console.log('✅ Removed from Favorites Success:', data);
      return data;
    } catch (error) {
      console.error('❌ Remove from Favorites Failed:', error.message);
      throw error;
    }
  },

  // 6. Advanced search with filters
  advancedSearch: async (filters) => {
    console.log('🔍 Advanced Search with filters:', filters);
    try {
      const data = await apiRequest('/api/stations/advanced-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
      });
      console.log('🎵 Advanced Search Success:', data?.length || 0, 'stations found');
      return data;
    } catch (error) {
      console.error('❌ Advanced Search Failed:', error.message);
      throw error;
    }
  },

  // 7. Record station listen
  recordListen: async (stationId, userId) => {
    console.log('🎧 Recording Listen:', { stationId, userId });
    try {
      const data = await apiRequest('/api/stations/listen', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stationId,
          userId
        })
      });
      console.log('✅ Listen Recorded Success:', data);
      return data;
    } catch (error) {
      console.error('❌ Record Listen Failed:', error.message);
      throw error;
    }
  },

  // 8. Vote for station
  voteStation: async (stationId, userId, rating) => {
    console.log('⭐ Voting for Station:', { stationId, userId, rating });
    try {
      const data = await apiRequest('/api/stations/vote', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          stationId,
          userId,
          rating
        })
      });
      console.log('✅ Vote Success:', data);
      return data;
    } catch (error) {
      console.error('❌ Vote Failed:', error.message);
      throw error;
    }
  },

  // 9. Get user favorites
  getUserFavorites: async (userId) => {
    console.log('❤️ Getting User Favorites for:', userId);
    try {
      const data = await apiRequest('/api/user/favorites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      });
      console.log('✅ User Favorites Success:', data?.length || 0, 'favorites found');
      return data;
    } catch (error) {
      console.error('❌ Get User Favorites Failed:', error.message);
      throw error;
    }
  }
};

export default radioApi;

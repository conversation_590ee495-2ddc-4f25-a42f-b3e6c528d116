// Radio Browser API base URL
const BASE_URL = 'https://de1.api.radio-browser.info';

// Fetch wrapper function with detailed logging
const apiRequest = async (endpoint, options = {}) => {
  const url = `${BASE_URL}${endpoint}`;
  const config = {
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'RadioBrowserApp/1.0.0',
    },
    ...options,
  };

  console.log('🔗 API Request:', {
    url,
    method: config.method || 'GET',
    timestamp: new Date().toISOString()
  });

  try {
    const startTime = Date.now();
    const response = await fetch(url, config);
    const endTime = Date.now();

    console.log('📡 API Response:', {
      url,
      status: response.status,
      statusText: response.statusText,
      duration: `${endTime - startTime}ms`,
      timestamp: new Date().toISOString()
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ API Data received:', {
      url,
      dataLength: Array.isArray(data) ? data.length : 'object',
      firstItem: Array.isArray(data) && data.length > 0 ? data[0] : null,
      timestamp: new Date().toISOString()
    });

    return data;
  } catch (error) {
    console.error('❌ API Request Error:', {
      url,
      error: error.message,
      timestamp: new Date().toISOString()
    });
    throw error;
  }
};

// Simplified API Functions - Only 3 Essential Endpoints
export const radioApi = {

  // 1. Get top/popular stations (Most Important)
  getTopStations: async (limit = 10) => {
    console.log('🎯 Getting Top Stations...');
    try {
      const data = await apiRequest(`/json/stations/topvote/${limit}`);
      console.log('🎵 Top Stations Success:', data?.length || 0, 'stations received');
      return data;
    } catch (error) {
      console.error('❌ Top Stations Failed:', error.message);
      throw error;
    }
  },

  // 2. Search stations by name (Essential for user interaction)
  searchStations: async (query, limit = 10) => {
    console.log('🔍 Searching Stations for:', query);
    try {
      const data = await apiRequest(`/json/stations/byname/${encodeURIComponent(query)}?limit=${limit}`);
      console.log('🎵 Search Success:', data?.length || 0, 'stations found for:', query);
      return data;
    } catch (error) {
      console.error('❌ Search Failed for:', query, error.message);
      throw error;
    }
  },

  // 3. Get stations by country (Popular feature)
  getStationsByCountry: async (country, limit = 10) => {
    console.log('🌍 Getting Stations for Country:', country);
    try {
      const data = await apiRequest(`/json/stations/bycountry/${encodeURIComponent(country)}?limit=${limit}`);
      console.log('🎵 Country Stations Success:', data?.length || 0, 'stations for:', country);
      return data;
    } catch (error) {
      console.error('❌ Country Stations Failed for:', country, error.message);
      throw error;
    }
  }
};

export default radioApi;

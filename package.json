{"name": "firstapp", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-navigation/native": "^6.1.6", "@react-navigation/native-stack": "^6.9.12", "expo": "~48.0.18", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.14", "react-native-screens": "~3.20.0", "react-native-safe-area-context": "4.5.0"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}
import { StatusBar } from 'expo-status-bar';
import { StyleSheet } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import HomeScreen from './src/screens/HomeScreen';
import SearchScreen from './src/screens/SearchScreen';
import CategoriesScreen from './src/screens/CategoriesScreen';
import StationListScreen from './src/screens/StationListScreen';
import StationDetailScreen from './src/screens/StationDetailScreen';
import FavoritesScreen from './src/screens/FavoritesScreen';

const Stack = createNativeStackNavigator();
export default function App() {
  return (
    <NavigationContainer>
      <StatusBar style="auto" />
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#007AFF',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{ title: '📻 Radio Browser' }}
        />
        <Stack.Screen
          name="Search"
          component={SearchScreen}
          options={{ title: '🔍 Arama' }}
        />
        <Stack.Screen
          name="Categories"
          component={CategoriesScreen}
          options={{ title: '📂 Kategoriler' }}
        />
        <Stack.Screen
          name="StationList"
          component={StationListScreen}
        />
        <Stack.Screen
          name="StationDetail"
          component={StationDetailScreen}
        />
        <Stack.Screen
          name="Favorites"
          component={FavoritesScreen}
          options={{ title: '❤️ Favoriler' }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({});

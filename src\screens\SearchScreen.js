import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { radioApi } from '../services/radioApi';

export default function SearchScreen({ navigation, route }) {
  const [searchResults, setSearchResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchQuery, setSearchQuery] = useState(route.params?.query || '');

  useEffect(() => {
    if (route.params?.query) {
      handleSearch(route.params.query);
    }
  }, [route.params?.query]);

  const handleSearch = async (query = searchQuery) => {
    if (!query.trim()) return;

    try {
      setLoading(true);
      const results = await radioApi.searchStations(query.trim(), 50);
      setSearchResults(results);
    } catch (error) {
      Alert.alert('Hata', '<PERSON><PERSON> sı<PERSON>ında bir hata olu<PERSON>');
      console.error('Search error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStationPress = (station) => {
    navigation.navigate('StationDetail', { station });
  };

  const renderStation = ({ item }) => (
    <TouchableOpacity
      style={styles.stationCard}
      onPress={() => handleStationPress(item)}
    >
      <View style={styles.stationInfo}>
        <Image
          source={{ uri: item.favicon || 'https://via.placeholder.com/50' }}
          style={styles.stationImage}
          defaultSource={{ uri: 'https://via.placeholder.com/50' }}
        />
        <View style={styles.stationDetails}>
          <Text style={styles.stationName} numberOfLines={1}>
            {item.name}
          </Text>
          <Text style={styles.stationCountry} numberOfLines={1}>
            {item.country} • {item.tags}
          </Text>
          <Text style={styles.stationVotes}>
            ❤️ {item.votes} • 👥 {item.clickcount}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyText}>
        {searchQuery ? 'Sonuç bulunamadı' : 'İstasyon aramak için yukarıdaki arama kutusunu kullanın'}
      </Text>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="İstasyon ara..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={() => handleSearch()}
          returnKeyType="search"
          autoFocus={!route.params?.query}
        />
        <TouchableOpacity 
          style={styles.searchButton} 
          onPress={() => handleSearch()}
        >
          <Text style={styles.searchButtonText}>🔍</Text>
        </TouchableOpacity>
      </View>

      {/* Results */}
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Aranıyor...</Text>
        </View>
      ) : (
        <FlatList
          data={searchResults}
          renderItem={renderStation}
          keyExtractor={(item) => item.stationuuid}
          showsVerticalScrollIndicator={false}
          ListEmptyComponent={renderEmptyState}
          contentContainerStyle={searchResults.length === 0 ? styles.emptyList : null}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
    padding: 16,
  },
  searchContainer: {
    flexDirection: 'row',
    marginBottom: 16,
    backgroundColor: '#fff',
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInput: {
    flex: 1,
    padding: 12,
    fontSize: 16,
    borderRadius: 10,
  },
  searchButton: {
    padding: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  searchButtonText: {
    fontSize: 18,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  stationCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  stationInfo: {
    flexDirection: 'row',
    padding: 12,
    alignItems: 'center',
  },
  stationImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  stationDetails: {
    flex: 1,
  },
  stationName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  stationCountry: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  stationVotes: {
    fontSize: 12,
    color: '#999',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  emptyList: {
    flex: 1,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
});
